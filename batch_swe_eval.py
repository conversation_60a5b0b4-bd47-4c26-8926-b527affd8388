#!/usr/bin/env python3
"""
批量SWE-bench评估脚本
读取swe_eval.json文件，自动运行docker mini swe agent处理每个instance
"""

import json
import os
import sys
import subprocess
import time
import argparse
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # 如果没有安装 python-dotenv，忽略


class SWEBatchEvaluator:
    def __init__(self, swe_eval_file: str, output_dir: str = "./swe_results",
                 model_name: str = "gpt-4o", max_iterations: int = 30,
                 csv_use_docker: bool = False):
        self.swe_eval_file = Path(swe_eval_file)
        self.output_dir = Path(output_dir)
        self.model_name = model_name
        self.max_iterations = max_iterations
        self.csv_use_docker = csv_use_docker  # CSV数据是否使用Docker模式
        self.results_dir = self.output_dir / "results"
        self.patches_dir = self.output_dir / "patches"

        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)
        self.patches_dir.mkdir(exist_ok=True)

        # 日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.output_dir / f"batch_eval_{timestamp}.log"

    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)

        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")

    def convert_instance_id(self, instance_id: str) -> str:
        """转换instance_id格式：双下划线改为单下划线"""
        return instance_id.replace("__", "_")

    def get_docker_image(self, instance_id: str) -> str:
        """根据instance_id生成docker镜像名"""
        # 格式: swebench/sweb.eval.x86_64.{repo}_1776_{repo}-{issue_number}
        # 例如: django__django-13757 -> swebench/sweb.eval.x86_64.django_1776_django-13757
        parts = instance_id.split('__')
        repo_name = parts[0]  # django
        repo_issue = parts[1]  # django-13757
        return f"swebench/sweb.eval.x86_64.{repo_name}_1776_{repo_issue}"

    def _run_csv_instance(self, instance: Dict[str, Any], use_docker: bool = False) -> Dict[str, Any]:
        """处理CSV格式的实例（支持Docker和非Docker模式）"""
        instance_id = instance["instance_id"]
        problem_statement = instance["problem_statement"]

        mode = "Docker" if use_docker else "本地"
        self.log(f"使用CSV-{mode}模式处理instance: {instance_id}")

        start_time = time.time()

        try:
            # 准备输出目录
            instance_output_dir = self.results_dir / instance_id
            instance_output_dir.mkdir(exist_ok=True)

            if use_docker:
                # Docker模式：使用docker_main.py
                return self._run_csv_docker_instance(instance, instance_output_dir, start_time)
            else:
                # 本地模式：使用agent.py
                return self._run_csv_local_instance(instance, instance_output_dir, start_time)

        except Exception as e:
            execution_time = time.time() - start_time
            self.log(f"CSV模式处理instance {instance_id} 时出错: {e}", "ERROR")
            return {
                "instance_id": instance_id,
                "model_name_or_path": self.model_name,
                "model_patch": "",
                "success": False,
                "execution_time": execution_time,
                "error": str(e)
            }

    def _run_csv_local_instance(self, instance: Dict[str, Any], instance_output_dir: Path, start_time: float) -> Dict[str, Any]:
        """CSV本地模式处理"""
        instance_id = instance["instance_id"]
        problem_statement = instance["problem_statement"]

        # 使用我们的agent.py中的MiniSWEAgent
        from agent import MiniSWEAgent

        # 创建临时CSV文件，只包含当前实例
        temp_csv_file = instance_output_dir / "temp_instance.csv"
        import pandas as pd
        temp_df = pd.DataFrame([instance])
        temp_df.to_csv(temp_csv_file, index=False)

        # 创建agent
        agent = MiniSWEAgent(
            repo_path=Path("."),  # 使用当前目录作为工作目录
            model_name=self.model_name,
            max_iterations=self.max_iterations,
            output_dir=instance_output_dir,
            csv_file_path=str(temp_csv_file)
        )

        # 运行agent
        result = agent.run(problem_statement, instance_index=0)

        execution_time = time.time() - start_time

        # 构建结果
        # 优先使用patch字段，如果没有则使用summary
        patch = result.get("patch", "") or result.get("summary", "")

        result_data = {
            "instance_id": instance_id,
            "model_name_or_path": self.model_name,
            "model_patch": patch,
            "success": result.get("success", False),
            "execution_time": execution_time,
            "stdout": f"CSV本地模式执行完成，迭代次数: {result.get('iterations', 0)}",
            "stderr": result.get("error", "") if not result.get("success", False) else ""
        }

        # 清理临时文件
        try:
            temp_csv_file.unlink()
        except:
            pass

        return result_data

    def _run_csv_docker_instance(self, instance: Dict[str, Any], instance_output_dir: Path, start_time: float) -> Dict[str, Any]:
        """CSV Docker模式处理"""
        instance_id = instance["instance_id"]
        problem_statement = instance["problem_statement"]

        # 创建临时CSV文件，只包含当前实例
        temp_csv_file = instance_output_dir / "temp_instance.csv"
        import pandas as pd
        temp_df = pd.DataFrame([instance])
        temp_df.to_csv(temp_csv_file, index=False)

        # 生成问题文件
        problem_file = instance_output_dir / "problem.txt"
        with open(problem_file, "w", encoding="utf-8") as f:
            f.write(problem_statement)

        # 生成对应的SWE-bench Docker镜像
        docker_image = instance.get('docker_image')
        if not docker_image:
            # 如果CSV中没有指定docker_image，根据instance_id生成SWE-bench镜像
            docker_image = self.get_docker_image(instance_id)
            self.log(f"根据instance_id生成Docker镜像: {docker_image}")

        # 生成容器名
        container_name = f"swe-eval-csv-{instance_id.replace('/', '-').replace('_', '-')}"

        try:
            # 运行docker main.py，传递CSV文件路径
            cmd = [
                "python3", "docker_main.py",
                "--docker_image", docker_image,
                "--problem", str(problem_file),
                "--model", self.model_name,
                "--max_iterations", str(self.max_iterations),
                "--output_dir", str(instance_output_dir),
                "--container_name", container_name,
                "--work_dir", "/testbed",
                "--csv_file", str(temp_csv_file),  # 传递CSV文件
                "--instance_index", "0"  # 使用第一行数据
            ]

            # 添加 API 密钥和 base URL 参数
            api_key = os.getenv("OPENAI_API_KEY")
            base_url = os.getenv("OPENAI_BASE_URL")

            if not api_key or not base_url:
                try:
                    from dotenv import load_dotenv
                    load_dotenv(override=True)
                    api_key = api_key or os.getenv("OPENAI_API_KEY")
                    base_url = base_url or os.getenv("OPENAI_BASE_URL")
                except ImportError:
                    pass

            if not api_key:
                api_key = "1830498799983480864"
                self.log("使用默认API密钥", "WARNING")
            if not base_url:
                base_url = "https://aigc.sankuai.com/v1/openai/native"
                self.log("使用默认Base URL", "WARNING")

            if api_key:
                cmd.extend(["--api_key", api_key])
            if base_url:
                cmd.extend(["--base_url", base_url])

            self.log(f"执行Docker CSV命令: {' '.join(cmd)}")

            # 准备环境变量
            env = os.environ.copy()
            if api_key:
                env["OPENAI_API_KEY"] = api_key
            if base_url:
                env["OPENAI_BASE_URL"] = base_url

            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600,  # 1小时超时
                env=env,
                encoding='utf-8',
                errors='replace'
            )

            execution_time = time.time() - start_time
            success = result.returncode == 0

            # 记录详细的执行结果
            if success:
                self.log(f"Docker命令执行成功，耗时: {execution_time:.2f}秒")
            else:
                self.log(f"Docker命令执行失败，返回码: {result.returncode}，耗时: {execution_time:.2f}秒", "ERROR")
                self.log(f"标准错误输出: {result.stderr}", "ERROR")
                if result.stdout:
                    self.log(f"标准输出: {result.stdout}", "WARNING")

            # 尝试提取patch
            patch = ""
            if success and result.stdout:
                try:
                    stdout = result.stdout
                    start_marker = "PATCH_START"
                    end_marker = "PATCH_END"

                    start_idx = stdout.find(start_marker)
                    end_idx = stdout.find(end_marker)

                    if start_idx != -1 and end_idx != -1:
                        patch_content = stdout[start_idx + len(start_marker):end_idx].strip()
                        if patch_content:
                            patch = patch_content
                            self.log(f"成功从Docker输出中提取patch，长度: {len(patch)}字符")
                        else:
                            self.log("Docker输出中的patch内容为空", "WARNING")
                    else:
                        self.log("Docker输出中未找到patch标记", "WARNING")

                except Exception as e:
                    self.log(f"从Docker输出中提取patch时出错: {e}", "ERROR")

            # 构建结果
            result_data = {
                "instance_id": instance_id,
                "model_name_or_path": self.model_name,
                "model_patch": patch,
                "success": success,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr
            }

            # 清理临时文件
            try:
                temp_csv_file.unlink()
            except:
                pass

            return result_data

        except subprocess.TimeoutExpired:
            self.log(f"CSV Docker Instance {instance_id} 执行超时", "ERROR")
            return {
                "instance_id": instance_id,
                "model_name_or_path": self.model_name,
                "model_patch": "",
                "success": False,
                "execution_time": 3600,
                "error": "执行超时"
            }
        except Exception as e:
            execution_time = time.time() - start_time
            self.log(f"CSV Docker Instance {instance_id} 执行异常: {e}", "ERROR")
            return {
                "instance_id": instance_id,
                "model_name_or_path": self.model_name,
                "model_patch": "",
                "success": False,
                "execution_time": execution_time,
                "error": str(e)
            }
        finally:
            # 清理容器
            try:
                subprocess.run(f"docker rm -f {container_name}", shell=True, capture_output=True)
            except:
                pass

    def cleanup_containers(self):
        """清理SWE评估相关的docker容器"""
        try:
            self.log("清理SWE评估相关的Docker容器...")

            # 查找所有以 swe-eval- 开头的容器
            find_cmd = "docker ps -aq --filter name=swe-eval-"
            result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)

            if result.stdout.strip():
                container_ids = result.stdout.strip().split('\n')
                self.log(f"找到 {len(container_ids)} 个SWE评估容器")

                # 停止这些容器
                stop_cmd = f"docker stop {' '.join(container_ids)}"
                subprocess.run(stop_cmd, shell=True, capture_output=True, text=True)

                # 删除这些容器
                rm_cmd = f"docker rm {' '.join(container_ids)}"
                subprocess.run(rm_cmd, shell=True, capture_output=True, text=True)

                self.log("SWE评估Docker容器清理完成")
            else:
                self.log("没有找到需要清理的SWE评估容器")

        except Exception as e:
            self.log(f"清理容器时出错: {e}", "WARNING")

    def extract_patch_from_container(self, container_name: str) -> str:
        """从容器中提取git diff"""
        try:
            # 在容器中生成git diff
            cmd = f"docker exec {container_name} bash -c 'cd /testbed && git diff --no-index --no-prefix /dev/null . || git diff HEAD'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)

            if result.stdout.strip():
                return result.stdout.strip()

            # 如果上面的命令没有输出，尝试其他方法
            cmd = f"docker exec {container_name} bash -c 'cd /testbed && git diff --cached'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)

            if result.stdout.strip():
                return result.stdout.strip()

            # 最后尝试获取所有未提交的更改
            cmd = f"docker exec {container_name} bash -c 'cd /testbed && git add -A && git diff --cached'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)

            return result.stdout.strip() if result.stdout.strip() else ""

        except Exception as e:
            self.log(f"提取patch时出错: {e}", "ERROR")
            return ""

    def run_single_instance(self, instance: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个instance的评估"""
        instance_id = instance["instance_id"]
        problem_statement = instance["problem_statement"]

        self.log(f"开始处理instance: {instance_id}")

        # 检查是否为CSV格式的数据（包含content字段）
        is_csv_data = 'content' in instance and instance['content']

        if is_csv_data:
            # 强制使用Docker模式处理CSV数据
            self.log(f"检测到CSV数据，强制使用CSV-Docker模式处理")
            return self._run_csv_instance(instance, use_docker=True)

        # 原有的Docker模式处理
        # 生成docker镜像名
        docker_image = self.get_docker_image(instance_id)
        self.log(f"使用Docker镜像: {docker_image}")

        # 检查镜像是否存在（可选，因为网络问题可能导致检查失败）
        try:
            check_image_cmd = f"docker images -q {docker_image}"
            check_result = subprocess.run(check_image_cmd, shell=True, capture_output=True, text=True, timeout=10)
            if not check_result.stdout.strip():
                self.log(f"本地未找到镜像 {docker_image}，将尝试从远程拉取", "WARNING")
        except Exception as e:
            self.log(f"检查镜像时出错: {e}", "WARNING")

        # 准备输出目录
        instance_output_dir = self.results_dir / instance_id
        instance_output_dir.mkdir(exist_ok=True)

        # 生成问题文件
        problem_file = instance_output_dir / "problem.txt"
        with open(problem_file, "w", encoding="utf-8") as f:
            f.write(problem_statement)

        # 生成容器名
        container_name = f"swe-eval-{instance_id.replace('/', '-').replace('_', '-')}"

        try:
            # 运行docker mini swe agent
            cmd = [
                "python3", "docker_main.py",
                "--docker_image", docker_image,
                "--problem", str(problem_file),
                "--model", self.model_name,
                "--max_iterations", str(self.max_iterations),
                "--output_dir", str(instance_output_dir),
                "--container_name", container_name,
                "--work_dir", "/testbed"
            ]

            # 添加 API 密钥和 base URL 参数
            api_key = os.getenv("OPENAI_API_KEY")
            base_url = os.getenv("OPENAI_BASE_URL")

            # 如果环境变量为空，尝试从 .env 文件重新加载
            if not api_key or not base_url:
                try:
                    from dotenv import load_dotenv
                    load_dotenv(override=True)  # 强制重新加载
                    api_key = api_key or os.getenv("OPENAI_API_KEY")
                    base_url = base_url or os.getenv("OPENAI_BASE_URL")
                except ImportError:
                    pass

            # 如果还是没有，使用默认值
            if not api_key:
                api_key = "1830498799983480864"
                self.log("使用默认API密钥", "WARNING")
            if not base_url:
                base_url = "https://aigc.sankuai.com/v1/openai/native"
                self.log("使用默认Base URL", "WARNING")

            if api_key:
                cmd.extend(["--api_key", api_key])
            if base_url:
                cmd.extend(["--base_url", base_url])

            self.log(f"API密钥: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
            self.log(f"Base URL: {base_url}")

            self.log(f"执行命令: {' '.join(cmd)}")

            # 准备环境变量
            env = os.environ.copy()
            if api_key:
                env['OPENAI_API_KEY'] = api_key
            if base_url:
                env['OPENAI_BASE_URL'] = base_url

            # 执行命令
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600, env=env)  # 1小时超时
            end_time = time.time()

            execution_time = end_time - start_time
            self.log(f"执行完成，耗时: {execution_time:.2f}秒")

            # 检查执行结果
            success = result.returncode == 0

            if success:
                self.log(f"Instance {instance_id} 执行成功")
            else:
                error_msg = result.stderr
                self.log(f"Instance {instance_id} 执行失败: {error_msg}", "ERROR")

                # 检查是否是 Docker 镜像相关的错误
                if "Unable to find image" in error_msg or "i/o timeout" in error_msg:
                    self.log("Docker 镜像问题检测到，可能的解决方案:", "INFO")
                    self.log(f"1. 检查网络连接到 Docker Hub", "INFO")
                    self.log(f"2. 尝试手动拉取镜像: docker pull {docker_image}", "INFO")
                    self.log(f"3. 检查镜像名称是否正确", "INFO")

                    # 建议可能的正确镜像名称
                    repo_name = instance_id.split('__')[0].replace('_', '/')
                    issue_num = instance_id.split('-')[-1]
                    suggested_image = f"swebench/sweb.eval.x86_64.{repo_name.replace('/', '_')}_XXXX_{repo_name.replace('/', '-')}-{issue_num}"
                    self.log(f"4. 可能的正确镜像格式: {suggested_image}", "INFO")

            # 从输出中提取patch
            patch = ""
            if success and result.stdout:
                try:
                    # 从 stdout 中提取 PATCH_START 和 PATCH_END 之间的内容
                    stdout = result.stdout
                    start_marker = "PATCH_START"
                    end_marker = "PATCH_END"

                    start_idx = stdout.find(start_marker)
                    end_idx = stdout.find(end_marker)

                    if start_idx != -1 and end_idx != -1:
                        patch_content = stdout[start_idx + len(start_marker):end_idx].strip()
                        if patch_content:
                            patch = patch_content
                            self.log(f"成功从输出中提取patch，长度: {len(patch)}字符")
                        else:
                            self.log("输出中的patch内容为空", "WARNING")
                    else:
                        self.log("输出中未找到patch标记", "WARNING")

                except Exception as e:
                    self.log(f"从输出中提取patch时出错: {e}", "ERROR")

            # 构建结果
            result_data = {
                "instance_id": instance_id,
                "model_name_or_path": self.model_name,
                "model_patch": patch,
                "success": success,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr
            }

            return result_data

        except subprocess.TimeoutExpired:
            self.log(f"Instance {instance_id} 执行超时", "ERROR")
            return {
                "instance_id": instance_id,
                "model_name_or_path": self.model_name,
                "model_patch": "",
                "success": False,
                "execution_time": 3600,
                "error": "执行超时"
            }
        except Exception as e:
            self.log(f"Instance {instance_id} 执行异常: {e}", "ERROR")
            return {
                "instance_id": instance_id,
                "model_name_or_path": self.model_name,
                "model_patch": "",
                "success": False,
                "execution_time": 0,
                "error": str(e)
            }
        finally:
            # 清理单个容器
            try:
                subprocess.run(f"docker rm -f {container_name}", shell=True, capture_output=True)
            except:
                pass

    def save_result(self, instance_id: str, result_data: Dict[str, Any]):
        """保存单个结果到jsonl文件"""
        file_name = instance_id.split("__")[-1] + ".jsonl"
        output_file = self.patches_dir / file_name

        # 只保存需要的字段到jsonl
        jsonl_data = {
            "instance_id": result_data["instance_id"],
            "model_name_or_path": result_data["model_name_or_path"],
            "model_path": result_data["model_patch"]  # 改为 model_path
        }

        try:
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(jsonl_data, f, ensure_ascii=False)
            self.log(f"结果已保存到: {output_file}")
        except Exception as e:
            self.log(f"保存结果时出错: {e}", "ERROR")

    def run_batch_evaluation(self, start_index: int = 0, max_instances: int = None):
        """运行批量评估"""
        # 读取输入文件（支持JSON和CSV格式）
        try:
            if str(self.swe_eval_file).endswith('.csv'):
                # 处理CSV文件
                import pandas as pd
                df = pd.read_csv(self.swe_eval_file)
                instances = []
                for _, row in df.iterrows():
                    # 从CSV转换为批量评估需要的格式
                    instance = {
                        "instance_id": row.get('instance_id', f"csv_instance_{len(instances)}"),
                        "repo": row.get('repo', 'unknown/unknown'),
                        "base_commit": row.get('base_commit', 'main'),
                        "problem_statement": str(row['problem_statement']),
                        "content": str(row.get('content', ''))  # CSV特有字段
                    }
                    instances.append(instance)
                self.log(f"成功读取CSV文件: {len(instances)} 个实例")
            else:
                # 处理JSON文件
                with open(self.swe_eval_file, "r", encoding="utf-8") as f:
                    instances = json.load(f)
                self.log(f"成功读取JSON文件: {len(instances)} 个实例")
        except Exception as e:
            self.log(f"读取文件失败: {e}", "ERROR")
            return

        # 限制处理的实例数量
        if max_instances:
            instances = instances[start_index:start_index + max_instances]
        else:
            instances = instances[start_index:]

        self.log(f"开始批量评估，共 {len(instances)} 个instances")
        self.log(f"输出目录: {self.output_dir}")

        # 初始清理容器
        self.cleanup_containers()

        # 处理每个instance
        success_count = 0
        total_count = len(instances)

        for i, instance in enumerate(instances):
            self.log(f"处理进度: {i+1}/{total_count}")

            try:
                result = self.run_single_instance(instance)

                # 保存结果
                self.save_result(instance["instance_id"], result)

                if result["success"]:
                    success_count += 1

                # 保存详细结果到结果目录（移除 stdout 和 stderr）
                clean_result = {
                    "instance_id": result["instance_id"],
                    "model_name_or_path": result["model_name_or_path"],
                    "model_path": result["model_patch"],
                    "success": result["success"],
                    "execution_time": result["execution_time"]
                }
                if not result["success"] and "error" in result:
                    clean_result["error"] = result["error"]

                instance_result_file = self.results_dir / f"{instance['instance_id']}_result.json"
                with open(instance_result_file, "w", encoding="utf-8") as f:
                    json.dump(clean_result, f, ensure_ascii=False, indent=2)

            except Exception as e:
                self.log(f"处理instance {instance['instance_id']} 时出错: {e}", "ERROR")

        # 最终清理
        self.cleanup_containers()

        # 输出统计信息
        self.log(f"批量评估完成!")
        self.log(f"总计: {total_count} 个instances")
        self.log(f"成功: {success_count} 个")
        self.log(f"失败: {total_count - success_count} 个")
        self.log(f"成功率: {success_count/total_count*100:.1f}%")


def main():
    parser = argparse.ArgumentParser(description="批量SWE-bench评估脚本")
    parser.add_argument("--swe_eval_file", required=True, help="swe_eval.json或CSV文件路径")
    parser.add_argument("--output_dir", default="./swe_results", help="输出目录")
    parser.add_argument("--model", default="gpt-4o", help="使用的模型")
    parser.add_argument("--max_iterations", type=int, default=30, help="最大迭代次数")
    parser.add_argument("--start_index", type=int, default=0, help="开始处理的索引")
    parser.add_argument("--max_instances", type=int, help="最大处理数量")
    parser.add_argument("--cleanup_only", action="store_true", help="仅清理容器后退出")
    parser.add_argument("--csv_use_docker", action="store_true", help="CSV数据使用Docker模式（默认使用本地模式）")

    args = parser.parse_args()

    evaluator = SWEBatchEvaluator(
        swe_eval_file=args.swe_eval_file,
        output_dir=args.output_dir,
        model_name=args.model,
        max_iterations=args.max_iterations,
        csv_use_docker=args.csv_use_docker
    )

    if args.cleanup_only:
        evaluator.cleanup_containers()
        return

    try:
        evaluator.run_batch_evaluation(
            start_index=args.start_index,
            max_instances=args.max_instances
        )
    except KeyboardInterrupt:
        print("\n用户中断执行")
        evaluator.cleanup_containers()
        sys.exit(1)
    except Exception as e:
        print(f"执行失败: {e}")
        evaluator.cleanup_containers()
        sys.exit(1)


if __name__ == "__main__":
    main()
