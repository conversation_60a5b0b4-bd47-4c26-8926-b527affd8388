"""
Docker环境下的Mini SWE-Agent工具实现
支持在docker容器中执行命令和编辑文件
"""

import os
import subprocess
import tempfile
import json
from pathlib import Path
from typing import Optional, List, Dict


class DockerManager:
    """Docker容器管理器"""

    def __init__(self, image_name: str, container_name: Optional[str] = None, work_dir: str = "/testbed"):
        self.image_name = image_name
        self.container_name = container_name or f"mini-swe-{os.getpid()}"
        self.work_dir = work_dir
        self.container_id = None
        self.is_running = False

    def start_container(self) -> str:
        """启动docker容器"""
        try:
            # 检查容器是否已存在
            check_cmd = f"docker ps -a --filter name={self.container_name} --format '{{{{.ID}}}}'"
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)

            if result.stdout.strip():
                # 容器已存在，删除旧容器
                subprocess.run(f"docker rm -f {self.container_name}", shell=True)

            # 检查镜像是否存在，如果不存在则拉取
            check_image_cmd = f"docker images -q {self.image_name}"
            check_result = subprocess.run(check_image_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if not check_result.stdout.strip():
                print(f"本地未找到镜像 {self.image_name}，开始拉取...")
                pull_cmd = f"docker pull {self.image_name}"
                pull_result = subprocess.run(pull_cmd, shell=True, capture_output=True, text=True, timeout=600)  # 10分钟超时

                if pull_result.returncode != 0:
                    raise Exception(f"拉取镜像失败: {pull_result.stderr}")
                print(f"镜像 {self.image_name} 拉取成功")

            # 启动新容器
            cmd = [
                "docker", "run", "-d",
                "--name", self.container_name,
                "-w", self.work_dir,
                "-it", self.image_name,
                "/bin/bash"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"启动容器失败: {result.stderr}")

            self.container_id = result.stdout.strip()
            self.is_running = True

            # 验证容器是否正常运行
            self.execute_command("echo 'Container started successfully'")

            return f"Docker容器已启动: {self.container_name} ({self.container_id[:12]})"

        except Exception as e:
            return f"启动Docker容器失败: {str(e)}"

    def stop_container(self) -> str:
        """停止并删除容器"""
        try:
            if self.container_name:
                subprocess.run(f"docker rm -f {self.container_name}", shell=True)
                self.is_running = False
                return f"Docker容器已停止: {self.container_name}"
            return "没有运行中的容器"
        except Exception as e:
            return f"停止容器失败: {str(e)}"

    def execute_command(self, command: str, timeout: int = 30) -> Dict[str, any]:
        """在容器中执行命令"""
        try:
            if not self.is_running:
                raise Exception("容器未运行")

            cmd = [
                "docker", "exec", "-w", self.work_dir,
                self.container_name, "/bin/bash", "-c", command
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='replace'
            )

            return {
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode,
                "success": result.returncode == 0
            }

        except subprocess.TimeoutExpired:
            return {
                "stdout": "",
                "stderr": f"命令执行超时 ({timeout}秒)",
                "returncode": -1,
                "success": False
            }
        except Exception as e:
            return {
                "stdout": "",
                "stderr": f"执行命令失败: {str(e)}",
                "returncode": -1,
                "success": False
            }

    def copy_file_to_container(self, local_path: str, container_path: str) -> bool:
        """复制文件到容器"""
        try:
            cmd = f"docker cp {local_path} {self.container_name}:{container_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False

    def copy_file_from_container(self, container_path: str, local_path: str) -> bool:
        """从容器复制文件"""
        try:
            cmd = f"docker cp {self.container_name}:{container_path} {local_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False


class DockerStrReplaceEditor:
    """Docker环境下的文件编辑工具"""

    def __init__(self, docker_manager: DockerManager):
        self.docker = docker_manager
        self.edit_history = []

    def _validate_parameters(self, command: str, path: str, file_text: Optional[str] = None,
                           old_str: Optional[str] = None, new_str: Optional[str] = None,
                           insert_line: Optional[int] = None, view_range: Optional[List[int]] = None) -> Optional[str]:
        """统一参数验证"""
        if command == "str_replace":
            if not old_str:
                return (
                    "❌ 参数错误: str_replace命令缺少必需的'old_str'参数\n"
                    "💡 使用建议:\n"
                    "  - old_str: 要替换的原始字符串（不能为空）\n"
                    "  - new_str: 替换后的新字符串（可选，默认为空字符串表示删除）\n"
                    "📝 示例: {\"command\": \"str_replace\", \"path\": \"file.py\", \"old_str\": \"old_code\", \"new_str\": \"new_code\"}"
                )
            if old_str.strip() == "":
                return (
                    "❌ 参数错误: old_str不能为空字符串或只包含空白字符\n"
                    "💡 请提供要替换的具体内容"
                )
        elif command == "create":
            if file_text is None:
                return (
                    "❌ 参数错误: create命令缺少'file_text'参数\n"
                    "💡 使用建议: 提供要创建的文件内容（可以为空字符串创建空文件）\n"
                    "📝 示例: {\"command\": \"create\", \"path\": \"new_file.py\", \"file_text\": \"print('Hello')\"}"
                )
        elif command == "insert":
            if insert_line is None:
                return (
                    "❌ 参数错误: insert命令缺少'insert_line'参数\n"
                    "💡 使用建议: 指定要插入内容的行号（0表示文件开头）\n"
                    "📝 示例: {\"command\": \"insert\", \"path\": \"file.py\", \"insert_line\": 5, \"new_str\": \"new_line\"}"
                )

        return None

    def execute(self, command: str, path: str, file_text: Optional[str] = None,
                old_str: Optional[str] = None, new_str: Optional[str] = None,
                insert_line: Optional[int] = None, view_range: Optional[List[int]] = None) -> str:
        """执行编辑命令"""

        if not self.docker.is_running:
            return "错误: Docker容器未运行"

        # 参数验证
        validation_error = self._validate_parameters(command, path, file_text, old_str, new_str, insert_line, view_range)
        if validation_error:
            return validation_error

        try:
            if command == "view":
                return self._view(path, view_range)
            elif command == "create":
                return self._create(path, file_text)
            elif command == "str_replace":
                return self._str_replace(path, old_str, new_str)
            elif command == "insert":
                return self._insert(path, insert_line, new_str)
            elif command == "undo_edit":
                return self._undo_edit(path)
            else:
                return f"错误: 未知命令 '{command}'"
        except Exception as e:
            return f"执行命令 '{command}' 时出错: {str(e)}"

    def _view(self, path: str, view_range: Optional[List[int]] = None) -> str:
        """查看文件或目录"""
        # 检查路径是否存在
        check_result = self.docker.execute_command(f"test -e {path}")
        if not check_result["success"]:
            return f"错误: 路径 '{path}' 不存在"

        # 检查是否为目录
        is_dir_result = self.docker.execute_command(f"test -d {path}")

        if is_dir_result["success"]:
            # 显示目录内容
            result = self.docker.execute_command(f"ls -la {path}")
            if result["success"]:
                return f"目录内容 '{path}':\n{result['stdout']}"
            else:
                return f"错误: 无法访问目录 '{path}': {result['stderr']}"
        else:
            # 显示文件内容
            if view_range:
                start_line = max(1, view_range[0])
                end_line = view_range[1] if view_range[1] != -1 else "$"
                cmd = f"sed -n '{start_line},{end_line}p' {path} | cat -n"
            else:
                cmd = f"cat -n {path}"

            result = self.docker.execute_command(cmd)
            if result["success"]:
                line_count_result = self.docker.execute_command(f"wc -l < {path}")
                total_lines = line_count_result["stdout"].strip() if line_count_result["success"] else "?"

                if view_range:
                    header = f"文件 '{path}' (行 {view_range[0]}-{view_range[1]}, 总行数: {total_lines}):\n"
                else:
                    header = f"文件 '{path}' (共 {total_lines} 行):\n"

                return header + result["stdout"]
            else:
                return f"错误: 无法读取文件 '{path}': {result['stderr']}"

    def _create(self, path: str, content: str) -> str:
        """创建新文件"""
        # 检查文件是否已存在
        check_result = self.docker.execute_command(f"test -e {path}")
        if check_result["success"]:
            return f"错误: 文件 '{path}' 已存在"

        # 创建父目录
        parent_dir = str(Path(path).parent)
        if parent_dir != ".":
            self.docker.execute_command(f"mkdir -p {parent_dir}")

        # 写入文件 - 使用heredoc避免引号问题
        content = content or ""
        escaped_content = content.replace("'", "'\"'\"'")  # 转义单引号
        cmd = f"cat > '{path}' << 'EOF'\n{content}\nEOF"

        result = self.docker.execute_command(cmd)
        if result["success"]:
            self.edit_history.append(("create", path, None))
            return f"成功创建文件 '{path}'"
        else:
            return f"错误: 创建文件 '{path}' 失败: {result['stderr']}"

    def _str_replace(self, path: str, old_str: str, new_str: str) -> str:
        """字符串替换"""
        # 参数验证已在execute方法中完成，这里直接执行替换逻辑

        # 检查文件是否存在
        check_result = self.docker.execute_command(f"test -f {path}")
        if not check_result["success"]:
            return f"错误: 文件 '{path}' 不存在"

        # 获取原文件内容备份
        backup_result = self.docker.execute_command(f"cat {path}")
        if not backup_result["success"]:
            return f"错误: 无法读取文件 '{path}'"

        original_content = backup_result["stdout"]

        # 检查old_str是否存在
        if old_str not in original_content:
            return f"错误: 在文件中找不到指定的字符串:\n{old_str}"

        # 检查old_str是否唯一
        count = original_content.count(old_str)
        if count > 1:
            return f"错误: 找到 {count} 个匹配项，请提供更具体的字符串以确保唯一性"

        # 备份到历史
        self.edit_history.append(("str_replace", path, original_content))

        # 执行替换 - 使用python进行精确替换
        new_str = new_str or ""
        python_cmd = f"""python3 -c "
import sys
with open('{path}', 'r', encoding='utf-8') as f:
    content = f.read()
new_content = content.replace('''{{old_str}}''', '''{{new_str}}''')
with open('{path}', 'w', encoding='utf-8') as f:
    f.write(new_content)
print('替换完成')
" """.format(old_str=old_str.replace("'", "'\"'\"'"),
             new_str=new_str.replace("'", "'\"'\"'"))

        result = self.docker.execute_command(python_cmd)
        if result["success"]:
            return f"成功替换文件 '{path}' 中的内容"
        else:
            return f"错误: 替换操作失败: {result['stderr']}"

    def _insert(self, path: str, insert_line: int, content: str) -> str:
        """在指定行后插入内容"""
        # 参数验证已在execute方法中完成，这里直接执行插入逻辑

        # 检查文件是否存在
        check_result = self.docker.execute_command(f"test -f {path}")
        if not check_result["success"]:
            return f"错误: 文件 '{path}' 不存在"

        # 获取文件行数
        line_count_result = self.docker.execute_command(f"wc -l < {path}")
        if not line_count_result["success"]:
            return f"错误: 无法获取文件行数"

        try:
            total_lines = int(line_count_result["stdout"].strip())
        except ValueError:
            return f"错误: 无法解析文件行数"

        # 检查行号有效性
        if insert_line < 0 or insert_line > total_lines:
            return f"错误: 行号 {insert_line} 超出文件范围 (0-{total_lines})"

        # 备份原文件
        backup_result = self.docker.execute_command(f"cat {path}")
        if backup_result["success"]:
            self.edit_history.append(("insert", path, backup_result["stdout"]))

        # 执行插入
        content = content or ""
        if insert_line == 0:
            # 在文件开头插入
            cmd = f"(echo '{content}'; cat {path}) > {path}.tmp && mv {path}.tmp {path}"
        else:
            # 在指定行后插入
            cmd = f"sed '{insert_line}a\\{content}' {path} > {path}.tmp && mv {path}.tmp {path}"

        result = self.docker.execute_command(cmd)
        if result["success"]:
            return f"成功在文件 '{path}' 第 {insert_line} 行后插入内容"
        else:
            return f"错误: 插入操作失败: {result['stderr']}"

    def _undo_edit(self, path: str) -> str:
        """撤销上次编辑"""
        # 查找该文件的最后一次编辑
        for i in range(len(self.edit_history) - 1, -1, -1):
            edit_type, edit_path, original_content = self.edit_history[i]
            if edit_path == path:
                try:
                    if edit_type == "create":
                        # 删除创建的文件
                        result = self.docker.execute_command(f"rm {path}")
                        if result["success"]:
                            self.edit_history.pop(i)
                            return f"成功撤销创建操作，删除文件 '{path}'"
                        else:
                            return f"错误: 删除文件失败: {result['stderr']}"
                    else:
                        # 恢复原始内容
                        escaped_content = original_content.replace("'", "'\"'\"'")
                        cmd = f"cat > '{path}' << 'EOF'\n{original_content}\nEOF"
                        result = self.docker.execute_command(cmd)
                        if result["success"]:
                            self.edit_history.pop(i)
                            return f"成功撤销文件 '{path}' 的上次编辑"
                        else:
                            return f"错误: 恢复文件失败: {result['stderr']}"
                except Exception as e:
                    return f"错误: 撤销操作失败: {str(e)}"

        return f"没有找到文件 '{path}' 的编辑历史"


class DockerBashTool:
    """Docker环境下的Bash命令执行工具"""

    def __init__(self, docker_manager: DockerManager):
        self.docker = docker_manager

        # 危险命令黑名单
        self.dangerous_commands = [
            'rm -rf /', 'rm -rf *', 'rm -rf ~',
            'sudo rm', 'sudo dd', 'sudo chmod 777 /',
            'format', 'fdisk', 'mkfs',
            'shutdown', 'reboot', 'halt',
            'docker', 'exit'  # 防止在容器内操作docker或退出
        ]

    def execute(self, command: str) -> str:
        """执行bash命令"""
        if not self.docker.is_running:
            return "错误: Docker容器未运行"

        if not command.strip():
            return "错误: 命令不能为空"

        # 安全检查
        if self._is_dangerous_command(command):
            return f"错误: 拒绝执行潜在危险命令: {command}"

        try:
            result = self.docker.execute_command(command, timeout=60)

            # 组织输出
            output_parts = []

            if result["stdout"]:
                output_parts.append(f"标准输出:\n{result['stdout']}")

            if result["stderr"]:
                output_parts.append(f"标准错误:\n{result['stderr']}")

            output_parts.append(f"退出码: {result['returncode']}")

            return "\n".join(output_parts)

        except Exception as e:
            return f"错误: 命令执行失败: {str(e)}"

    def _is_dangerous_command(self, command: str) -> bool:
        """检查是否为危险命令"""
        command_lower = command.lower().strip()

        for dangerous in self.dangerous_commands:
            if dangerous in command_lower:
                return True

        return False
