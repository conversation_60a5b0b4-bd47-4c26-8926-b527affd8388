"""
工具配置模块
定义所有可用的Function Calling工具
"""

from typing import Dict, List, Any


class ToolConfigs:
    """工具配置管理类"""

    @staticmethod
    def get_str_replace_editor_tool() -> Dict[str, Any]:
        """文件编辑工具配置"""
        return {
            "type": "function",
            "function": {
                "name": "str_replace_editor",
                "description": "文件编辑工具，支持查看、创建、编辑Docker容器中的文件",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "enum": ["view", "create", "str_replace", "insert", "undo_edit"],
                            "description": "要执行的命令"
                        },
                        "path": {
                            "type": "string",
                            "description": "文件或目录路径（相对于容器工作目录）"
                        },
                        "file_text": {
                            "type": "string",
                            "description": "创建文件时的内容"
                        },
                        "old_str": {
                            "type": "string",
                            "description": "要替换的旧字符串（str_replace命令必需）"
                        },
                        "new_str": {
                            "type": "string",
                            "description": "替换后的新字符串（str_replace命令必需，可以为空字符串表示删除）"
                        },
                        "insert_line": {
                            "type": "integer",
                            "description": "插入内容的行号"
                        },
                        "view_range": {
                            "type": "array",
                            "items": {"type": "integer"},
                            "description": "查看文件的行号范围"
                        }
                    },
                    "required": ["command", "path"]
                }
            }
        }

    @staticmethod
    def get_bash_tool() -> Dict[str, Any]:
        """Bash命令执行工具配置"""
        return {
            "type": "function",
            "function": {
                "name": "bash",
                "description": "在Docker容器中执行bash命令",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "要在容器中执行的bash命令"
                        }
                    },
                    "required": ["command"]
                }
            }
        }

    @staticmethod
    def get_submit_tool() -> Dict[str, Any]:
        """任务提交工具配置"""
        return {
            "type": "function",
            "function": {
                "name": "submit",
                "description": "提交任务完成结果",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "summary": {
                            "type": "string",
                            "description": "任务完成摘要"
                        }
                    },
                    "required": ["summary"]
                }
            }
        }

    @classmethod
    def get_all_tools(cls) -> List[Dict[str, Any]]:
        """获取所有工具配置"""
        return [
            cls.get_str_replace_editor_tool(),
            cls.get_bash_tool(),
            cls.get_submit_tool()
        ]

    @classmethod
    def get_custom_tools(cls, tool_names: List[str]) -> List[Dict[str, Any]]:
        """根据工具名称列表获取自定义工具配置"""
        available_tools = {
            "str_replace_editor": cls.get_str_replace_editor_tool(),
            "bash": cls.get_bash_tool(),
            "submit": cls.get_submit_tool()
        }

        return [available_tools[name] for name in tool_names if name in available_tools]
