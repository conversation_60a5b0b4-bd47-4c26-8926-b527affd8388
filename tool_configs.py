"""
Tool Configuration Module
Defines all available Function Calling tools
"""

from typing import Dict, List, Any


class ToolConfigs:
    """Tool configuration management class"""

    @staticmethod
    def get_str_replace_editor_tool() -> Dict[str, Any]:
        """File editing tool configuration"""
        return {
            "type": "function",
            "function": {
                "name": "str_replace_editor",
                "description": """Custom editing tool for viewing, creating and editing files in Docker container
* State is persistent across command calls and discussions with the user
* If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
* The `create` command cannot be used if the specified `path` already exists as a file
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* The `undo_edit` command will revert the last edit made to the file at `path`

Notes for using the `str_replace` command:
* The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespaces!
* If the `old_str` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique
* The `new_str` parameter should contain the edited lines that should replace the `old_str`""",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "enum": ["view", "create", "str_replace", "insert", "undo_edit"],
                            "description": "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`."
                        },
                        "path": {
                            "type": "string",
                            "description": "Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`."
                        },
                        "file_text": {
                            "type": "string",
                            "description": "Required parameter of `create` command, with the content of the file to be created."
                        },
                        "old_str": {
                            "type": "string",
                            "description": "Required parameter of `str_replace` command containing the string in `path` to replace."
                        },
                        "new_str": {
                            "type": "string",
                            "description": "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert."
                        },
                        "insert_line": {
                            "type": "integer",
                            "description": "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`."
                        },
                        "view_range": {
                            "type": "array",
                            "items": {"type": "integer"},
                            "description": "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file."
                        }
                    },
                    "required": ["command", "path"]
                }
            }
        }

    @staticmethod
    def get_bash_tool() -> Dict[str, Any]:
        """Bash command execution tool configuration"""
        return {
            "type": "function",
            "function": {
                "name": "bash",
                "description": """Run commands in a bash shell within Docker container
* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
* You don't have access to the internet via this tool.
* You do have access to a mirror of common linux and python packages via apt and pip.
* State is persistent across command calls and discussions with the user.
* To inspect a particular line range of a file, e.g. lines 10-25, try 'sed -n 10,25p /path/to/the/file'.
* Please avoid commands that may produce a very large amount of output.
* Please run long lived commands in the background, e.g. 'sleep 10 &' or start a server in the background.""",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "The bash command to run."
                        }
                    },
                    "required": ["command"]
                }
            }
        }

    @staticmethod
    def get_submit_tool() -> Dict[str, Any]:
        """Task submission tool configuration"""
        return {
            "type": "function",
            "function": {
                "name": "submit",
                "description": "Submit task completion results",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "summary": {
                            "type": "string",
                            "description": "Task completion summary"
                        }
                    },
                    "required": ["summary"]
                }
            }
        }

    @classmethod
    def get_all_tools(cls) -> List[Dict[str, Any]]:
        """Get all tool configurations"""
        return [
            cls.get_str_replace_editor_tool(),
            cls.get_bash_tool(),
            cls.get_submit_tool()
        ]

    @classmethod
    def get_custom_tools(cls, tool_names: List[str]) -> List[Dict[str, Any]]:
        """Get custom tool configurations based on tool name list"""
        available_tools = {
            "str_replace_editor": cls.get_str_replace_editor_tool(),
            "bash": cls.get_bash_tool(),
            "submit": cls.get_submit_tool()
        }

        return [available_tools[name] for name in tool_names if name in available_tools]
