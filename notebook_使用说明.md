# SWE-Batch Runner Notebook 使用说明

## 📖 概述

`swe_batch_runner.ipynb` 是一个Jupyter notebook，完全实现了 `swe_frun.sh` 脚本的功能，用于批量处理SWE-bench测试用例。

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保安装必要的依赖
pip install pandas openai python-dotenv jupyter

# 启动Jupyter
jupyter notebook swe_batch_runner.ipynb
```

### 2. 环境变量设置

在项目根目录创建 `.env` 文件：

```bash
# .env 文件内容
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=your_base_url_here
```

### 3. 运行notebook

按顺序执行所有cell，或者：
- 修改第2步中的 `CONFIG` 参数
- 重新运行第6步进行批量处理

## ⚙️ 配置参数

原脚本参数与notebook配置的对应关系：

| 原脚本参数 | Notebook配置 | 说明 |
|------------|--------------|------|
| `--swe_eval_file` | `swe_eval_file` | CSV文件路径 |
| `--output_dir` | `output_dir` | 输出目录 |
| `--start_index` | `start_index` | 开始索引 |
| `--max_instances` | `max_instances` | 最大处理数量 |
| `--model` | `model` | AI模型名称 |
| `--csv_use_docker` | `csv_use_docker` | 是否使用Docker |

## 📊 输出结果

notebook会生成以下文件：
- `batch_results_{timestamp}.json` - JSON格式结果
- `batch_results_{timestamp}.csv` - CSV格式结果
- `patches/` - 生成的补丁文件
- `summary_report_{timestamp}.txt` - 汇总报告

## 🔧 自定义运行

如需修改参数重新运行：

```python
# 修改CONFIG字典
CONFIG['start_index'] = 2
CONFIG['max_instances'] = 5
CONFIG['model'] = 'gpt-4o'

# 然后重新运行第6步的cell
```

## 💡 优势

相比原shell脚本，notebook提供：
- 📈 实时进度显示和统计
- 🔍 详细的结果分析
- 💾 多格式结果保存
- 🛠️ 灵活的参数调整
- 📊 可视化的执行过程

## 🐛 故障排除

常见问题：
1. **模块导入失败**: 确保所有Python文件在同一目录
2. **API密钥问题**: 检查 `.env` 文件设置
3. **Docker问题**: 确保Docker服务运行正常
4. **CSV文件问题**: 确认 `agentic_edit_prompt.csv` 存在

## 📝 注意事项

- 确保Docker环境已正确配置（如使用Docker模式）
- 处理大量实例时建议分批执行
- 注意API调用频率限制
- 及时清理Docker容器避免资源占用

## 🎯 示例用法

```python
# 快速配置示例
CONFIG = {
    'swe_eval_file': 'agentic_edit_prompt.csv',
    'output_dir': './swe_results',
    'start_index': 0,
    'max_instances': 2,
    'model': 'gpt-4o',
    'csv_use_docker': True,
    'max_iterations': 30
}
```

这个notebook让你能够更灵活地控制SWE-bench评估过程，同时提供了更好的用户体验和结果分析功能。
