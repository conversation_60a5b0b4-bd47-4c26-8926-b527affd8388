#!/bin/bash

# 批量SWE-bench评估启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "批量SWE-bench评估启动脚本"
    echo ""
    echo "用法:"
    echo "  $0 --swe_eval_file <文件路径> [选项]"
    echo ""
    echo "参数:"
    echo "  --swe_eval_file      swe_eval.json文件路径 (必需)"
    echo "  --output_dir         输出目录 (默认: ./swe_results)"
    echo "  --model              模型名称 (默认: gpt-4o)"
    echo "  --max_iterations     最大迭代次数 (默认: 30)"
    echo "  --start_index        开始处理的索引 (默认: 0)"
    echo "  --max_instances      最大处理数量"
    echo "  --cleanup_only       仅清理容器后退出"
    echo "  --help               显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --swe_eval_file swe_eval.json"
    echo "  $0 --swe_eval_file swe_eval.json --model gpt-4-turbo --max_instances 5"
    echo "  $0 --swe_eval_file swe_eval.json --start_index 10 --max_instances 20"
    echo "  $0 --cleanup_only  # 仅清理所有容器"
}

# 检查Docker环境
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_warning "Docker 未安装，将跳过Docker相关功能"
    elif ! docker info &> /dev/null; then
        print_warning "Docker服务未运行，将跳过Docker相关功能"
    else
        print_success "Docker 环境检查通过"
        return 0
    fi

    print_info "继续运行非Docker模式..."
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装，请先安装Python3"
        exit 1
    fi
    print_success "Python3 环境检查通过"
}

# 检查并安装依赖
check_dependencies() {
    print_info "检查依赖包..."

    if [ ! -f "requirements.txt" ]; then
        print_error "未找到requirements.txt文件"
        exit 1
    fi

    # 检查依赖包是否已安装
    print_info "检查Python依赖包..."
    python3 -c "import openai, dotenv, pandas" 2>/dev/null || {
        print_info "安装依赖包..."
        pip3 install -r requirements.txt > /dev/null 2>&1
        print_success "依赖包安装完成"
    }
    print_success "依赖包检查完成"
}

# 检查API密钥
check_api_key() {
    if [ -z "$OPENAI_API_KEY" ] && [ ! -f ".env" ]; then
        print_warning "未设置OPENAI_API_KEY环境变量"
        print_info "请设置环境变量或创建.env文件："
        echo "  export OPENAI_API_KEY='your-api-key'"
        echo "  或"
        echo "  echo 'OPENAI_API_KEY=your-api-key' > .env"
        read -p "是否继续运行? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_success "API密钥配置检查通过"
    fi
}

# 检查必需文件
check_required_files() {
    local files=("docker_tools.py" "docker_agent.py" "docker_main.py" "batch_swe_eval.py")

    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "缺少必需文件: $file"
            exit 1
        fi
    done

    print_success "必需文件检查通过"
}

# 主函数
main() {
    print_info "启动批量SWE-bench评估..."

    # 检查帮助参数
    for arg in "$@"; do
        if [ "$arg" = "--help" ] || [ "$arg" = "-h" ]; then
            show_help
            exit 0
        fi
    done

    # 解析swe_eval_file参数
    SWE_EVAL_FILE=""
    CLEANUP_ONLY=false

    for i in "$@"; do
        if [ "$prev_arg" = "--swe_eval_file" ]; then
            SWE_EVAL_FILE="$i"
        elif [ "$i" = "--cleanup_only" ]; then
            CLEANUP_ONLY=true
        fi
        prev_arg="$i"
    done

    # 如果只是清理容器，直接执行
    if [ "$CLEANUP_ONLY" = true ]; then
        print_info "执行容器清理..."
        python3 batch_swe_eval.py --cleanup_only
        exit 0
    fi

    # 检查必需参数
    if [ -z "$SWE_EVAL_FILE" ]; then
        print_error "缺少必需参数: --swe_eval_file"
        echo ""
        show_help
        exit 1
    fi

    # 检查文件是否存在
    if [ ! -f "$SWE_EVAL_FILE" ]; then
        print_error "文件不存在: $SWE_EVAL_FILE"
        exit 1
    fi

    # 环境检查
    check_docker
    check_python
    check_required_files
    check_dependencies
    check_api_key

    print_info "开始执行批量评估..."
    print_info "使用文件: $SWE_EVAL_FILE"
    print_info "强制启用Docker模式处理所有数据"

    # 执行批量评估，强制启用Docker模式
    python3 batch_swe_eval.py "$@" --csv_use_docker
}

# 错误处理
trap 'print_error "脚本执行失败"; exit 1' ERR

# 执行主函数
main "$@"
