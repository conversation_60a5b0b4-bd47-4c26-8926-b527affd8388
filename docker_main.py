#!/usr/bin/env python3
"""
Docker Mini SWE-Agent - 在Docker容器中运行的精简版本SWE-agent
使用str_replace_editor和bash工具在docker环境中完成编程任务
"""

import argparse
import os
import sys
from pathlib import Path
from docker_agent import DockerMiniSWEAgent


def main():
    parser = argparse.ArgumentParser(description="Docker Mini SWE-Agent - 在Docker容器中运行的编程助手")
    parser.add_argument("--docker_image", required=True, help="Docker镜像名称 (例如: swebench/sweb.eval.x86_64.astropy_1776_astropy-14365)")
    parser.add_argument("--problem", required=True, help="问题描述文件路径或直接描述")
    parser.add_argument("--model", default="gpt-4o", help="使用的模型 (默认: gpt-4o)")
    parser.add_argument("--max_iterations", type=int, default=20, help="最大迭代次数")
    parser.add_argument("--output_dir", default="./results", help="输出目录")
    parser.add_argument("--api_key", help="OpenAI API密钥 (可选，默认从环境变量读取)")
    parser.add_argument("--base_url", help="API基础URL (可选，默认使用OpenAI官方)")
    parser.add_argument("--max_retries", type=int, default=3, help="最大重试次数")
    parser.add_argument("--container_name", help="Docker容器名称 (可选，默认自动生成)")
    parser.add_argument("--work_dir", default="/testbed", help="容器内工作目录 (默认: /testbed)")
    parser.add_argument("--csv_file", help="CSV文件路径，包含problem_statement和content列")
    parser.add_argument("--instance_index", type=int, default=0, help="CSV文件中要使用的实例索引 (默认: 0)")

    args = parser.parse_args()

    # 读取问题描述
    if Path(args.problem).exists():
        problem_statement = Path(args.problem).read_text(encoding='utf-8')
    else:
        problem_statement = args.problem

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"🐳 启动Docker Mini SWE-Agent")
    print(f"📦 Docker镜像: {args.docker_image}")
    print(f"🤖 使用模型: {args.model}")
    print(f"📝 问题描述: {problem_statement[:100]}...")
    print(f"📊 输出目录: {output_dir}")
    print(f"🏠 容器工作目录: {args.work_dir}")
    if args.container_name:
        print(f"🏷️  容器名称: {args.container_name}")
    print("-" * 60)

    # 创建并运行agent
    agent = DockerMiniSWEAgent(
        docker_image=args.docker_image,
        model_name=args.model,
        max_iterations=args.max_iterations,
        output_dir=output_dir,
        api_key=args.api_key,
        base_url=args.base_url,
        max_retries=args.max_retries,
        container_name=args.container_name,
        work_dir=args.work_dir,
        csv_file_path=args.csv_file
    )

    try:
        result = agent.run(problem_statement, instance_index=args.instance_index)

        if result["success"]:
            print("✅ 任务完成成功!")
            print(f"📄 详细日志: {result['log_file']}")
            if result.get("llm_log_file"):
                print(f"🤖 LLM交互日志: {result['llm_log_file']}")
            print(f"🔄 迭代次数: {result.get('iterations', 0)}")

            # 调试信息：显示 result 的所有键
            print(f"🔍 调试 - result 包含的键: {list(result.keys())}")
            if "patch" in result:
                print(f"🔍 调试 - patch 值: '{result['patch']}'")
                print(f"🔍 调试 - patch 类型: {type(result['patch'])}")
                print(f"🔍 调试 - patch 长度: {len(result['patch']) if result['patch'] else 0}")

            # 输出生成的补丁
            if result.get("patch"):
                print(f"🔧 生成的补丁长度: {len(result['patch'])} 字符")
                print("=" * 50)
                print("PATCH_START")
                print(result["patch"])
                print("PATCH_END")
                print("=" * 50)
            else:
                print("⚠️  未生成补丁")
        else:
            print("❌ 任务执行失败")
            print(f"错误信息: {result.get('error', '未知错误')}")
            if result.get("log_file"):
                print(f"📄 错误日志: {result['log_file']}")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
