"""
提示词模板模块
管理所有系统提示词模板，便于维护和修改
"""

from typing import Dict, Any


class PromptTemplates:
    """提示词模板管理类"""

    @staticmethod
    def get_csv_based_prompt(problem_statement: str, content: str) -> str:
        """
        基于CSV数据的提示词模板
        用于处理带有problem_statement和content字段的CSV数据
        """
        return f"""

You are an AI software engineer tasked with solving a repository issue. You have access to the following tools:

**Available Tools:**

- `bash`: Execute shell commands to explore the repository, run tests, or perform system operations

- `str_replace_editor`: View, create, and edit files using search/replace operations

- `submit`: Submit your final solution when the issue is resolved

**Current Issue:**

--- BEGIN ISSUE ---

{problem_statement}

--- END ISSUE ---

**Target File Information:**

Below are some code segments, each from a relevant file. One or more of these files may contain bugs.

**File Content:**

--- BEGIN FILE ---

```

{content}

```

--- <PERSON>ND FILE ---

**Code Blocks Requiring Modification:**

In the file above, problematic code blocks are marked with "### chunk_to_be_modified_X:" format (where X is a number). Each marked block needs to be fixed to resolve the issue.

**Your Task:**

1. **Analyze the Issue**: First understand the problem by examining the issue description and the marked code blocks

2. **Investigate**: Use the `bash` tool to run tests, explore the codebase, or gather additional information if needed

3. **Implement Fixes**: Use the `str_replace_editor` tool to make precise changes to each marked code block

4. **Verify**: Test your changes to ensure they resolve the issue without breaking existing functionality

5. **Submit**: Use the `submit` tool when you're confident the issue is resolved

**Important Guidelines:**

- Focus only on the marked code blocks (### chunk_to_be_modified_X:)

- Make minimal, targeted changes that directly address the issue

- Preserve existing code formatting and indentation

- Test your changes before submitting

**Expected Workflow:**

1. Start by analyzing the issue and understanding what needs to be fixed

2. Use tools systematically to explore, modify, and verify your changes

3. Ensure each marked chunk_to_be_modified is properly addressed

4. Submit your solution only after thorough testing

Begin by examining the issue and the marked code blocks to understand what needs to be fixed.

"""

    @staticmethod
    def get_default_prompt(docker_image: str, work_dir: str, env_info: str) -> str:
        """
        默认提示词模板
        用于没有CSV数据时的通用场景
        """
        return f"""你是一个专业的软件工程师，需要在Docker容器环境中解决编程问题。

工作环境:
- Docker镜像: {docker_image}
- 工作目录: {work_dir}
- 环境信息: {env_info}

可用工具:
1. str_replace_editor: 文件编辑工具，支持查看、创建、编辑文件
2. bash: 在容器中执行bash命令
3. submit: 完成任务时提交结果

注意事项:
- 所有文件操作都在Docker容器中进行
- 文件路径相对于容器的工作目录 {work_dir}
- 在修改代码前先理解现有代码结构
- 修改后进行测试验证
- 完成任务后调用submit工具提交结果

请根据问题描述，分析代码，找出问题并实现解决方案。"""

    @staticmethod
    def get_environment_info_template(docker_image: str, work_dir: str, env_info: str) -> str:
        """
        环境信息模板
        可以附加到其他提示词后面
        """
        return f"""

工作环境:
- Docker镜像: {docker_image}
- 工作目录: {work_dir}
- 环境信息: {env_info}

可用工具:
1. str_replace_editor: 文件编辑工具，支持查看、创建、编辑文件
2. bash: 在容器中执行bash命令
3. submit: 完成任务时提交结果

注意事项:
- 所有文件操作都在Docker容器中进行
- 文件路径相对于容器的工作目录 {work_dir}
- 在修改代码前先理解现有代码结构
- 修改后进行测试验证
- 完成任务后调用submit工具提交结果

请根据问题描述，分析代码，找出问题并实现解决方案。"""

    @classmethod
    def build_system_prompt(cls, template_type: str = "csv", **kwargs) -> str:
        """
        构建系统提示词

        Args:
            template_type: 模板类型 ("csv", "default", "custom")
            **kwargs: 模板参数

        Returns:
            构建好的系统提示词
        """
        if template_type == "csv":
            problem_statement = kwargs.get("problem_statement", "")
            content = kwargs.get("content", "")
            docker_image = kwargs.get("docker_image", "")
            work_dir = kwargs.get("work_dir", "/testbed")
            env_info = kwargs.get("env_info", "")

            csv_prompt = cls.get_csv_based_prompt(problem_statement, content)
            env_prompt = cls.get_environment_info_template(docker_image, work_dir, env_info)
            return csv_prompt + env_prompt

        elif template_type == "default":
            docker_image = kwargs.get("docker_image", "")
            work_dir = kwargs.get("work_dir", "/testbed")
            env_info = kwargs.get("env_info", "")
            return cls.get_default_prompt(docker_image, work_dir, env_info)

        else:
            raise ValueError(f"不支持的模板类型: {template_type}")


class PromptConfig:
    """提示词配置类，用于管理不同场景下的提示词设置"""

    # 默认配置
    DEFAULT_CONFIG = {
        "temperature": 0.1,
        "max_tokens": None,
        "top_p": 1.0,
    }

    # CSV模式配置
    CSV_CONFIG = {
        "temperature": 0.1,
        "max_tokens": None,
        "top_p": 1.0,
    }

    @classmethod
    def get_config(cls, config_type: str = "default") -> Dict[str, Any]:
        """获取指定类型的配置"""
        configs = {
            "default": cls.DEFAULT_CONFIG,
            "csv": cls.CSV_CONFIG,
        }
        return configs.get(config_type, cls.DEFAULT_CONFIG)
