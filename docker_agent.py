"""
Docker环境下的Mini SWE-Agent核心类
处理与LLM的交互和docker容器中的工具调用
"""

import json
import os
import logging
import time
import random
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from openai import OpenAI
from dotenv import load_dotenv
from docker_tools import DockerManager
from tool_configs import ToolConfigs
from prompt_templates import PromptTemplates, PromptConfig
from tool_manager import ToolManager

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("docker_mini_swe_agent")


class DockerMiniSWEAgent:
    """Docker环境下的Mini SWE-Agent"""

    def __init__(self, docker_image: str, model_name: str, max_iterations: int, output_dir: Path,
                 api_key: str = None, base_url: str = None, max_retries: int = 3,
                 container_name: str = None, work_dir: str = "/testbed", csv_file_path: str = None):
        self.docker_image = docker_image
        self.model_name = model_name
        self.max_iterations = max_iterations
        self.output_dir = output_dir
        self.iteration_count = 0
        self.work_dir = work_dir
        self.csv_file_path = csv_file_path
        self.csv_data = None

        # 重试配置
        self.max_retries = max_retries
        self.initial_backoff = 2
        self.backoff_multiplier = 2
        self.jitter = 0.1

        # 文件修改跟踪 - 新增
        self.modified_source_files = set()
        self.last_git_status = ""
        self.important_modifications_detected = False

        # 初始化Docker管理器和工具管理器
        self.docker_manager = DockerManager(docker_image, container_name, work_dir)
        self.tool_manager = ToolManager(self.docker_manager)

        # 设置OpenAI客户端
        self.client = OpenAI(
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
            base_url=base_url or os.getenv("OPENAI_BASE_URL")
        )

        # 对话历史
        self.conversation_history = []

        # 日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = output_dir / f"docker_mini_swe_agent_{timestamp}.log"
        self.llm_interaction_log_file = output_dir / f"docker_llm_interactions_{timestamp}.log"

        # 获取工具配置
        self.tools = ToolConfigs.get_all_tools()

    def load_csv_data(self):
        """加载CSV文件数据"""
        if self.csv_file_path and self.csv_data is None:
            try:
                import pandas as pd
                self.csv_data = pd.read_csv(self.csv_file_path)
                self.log(f"成功加载CSV文件: {self.csv_file_path}, 共 {len(self.csv_data)} 行数据")
            except Exception as e:
                self.log(f"加载CSV文件失败: {e}", "ERROR")
                self.csv_data = None

    def get_system_prompt(self, instance_index: int = 0, env_info: str = "") -> str:
        """获取系统提示词 - 根据CSV数据和模板生成"""
        # 确保CSV数据已加载
        if self.csv_data is None and self.csv_file_path:
            self.load_csv_data()

        # 准备模板参数
        template_kwargs = {
            "docker_image": self.docker_image,
            "work_dir": self.work_dir,
            "env_info": env_info,
        }

        # 如果有CSV数据，使用CSV模板
        if self.csv_data is not None and len(self.csv_data) > 0:
            # 获取指定索引的数据
            if instance_index >= len(self.csv_data):
                self.log(f"索引 {instance_index} 超出数据范围，使用第一行数据", "WARNING")
                instance_index = 0

            row = self.csv_data.iloc[instance_index]
            template_kwargs.update({
                "problem_statement": str(row['problem_statement']),
                "content": str(row['content']),
            })

            return PromptTemplates.build_system_prompt("csv", **template_kwargs)
        else:
            # 使用默认模板
            return PromptTemplates.build_system_prompt("default", **template_kwargs)


    def _calculate_backoff(self, retry_count):
        """计算退避时间"""
        backoff = self.initial_backoff * (self.backoff_multiplier ** retry_count)
        jitter_amount = backoff * self.jitter * random.random()
        return backoff + jitter_amount

    def _should_retry(self, exception):
        """判断是否应该重试"""
        if hasattr(exception, "status_code") and exception.status_code == 429:
            return True
        if hasattr(exception, "status_code") and 500 <= exception.status_code < 600:
            return True
        if isinstance(exception, (ConnectionError, TimeoutError)):
            return True
        return False

    def log(self, message: str, level: str = "INFO"):
        """统一的日志记录方法"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        print(log_message)

        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_message + "\n")
        except Exception:
            pass

    def track_file_modifications(self):
        """跟踪文件修改状态"""
        try:
            # 获取当前git状态
            current_git_status = self.tool_manager.bash_tool.execute("cd /testbed && git status --porcelain")

            # 检查是否有新的修改
            if current_git_status != self.last_git_status:
                self.log(f"检测到文件修改变化")

                # 解析修改的文件
                if current_git_status.strip():
                    for line in current_git_status.strip().split('\n'):
                        if line.strip():
                            file_path = line.split()[-1] if line.split() else ""
                            if file_path and file_path.endswith('.py'):
                                # 检查是否是重要的源文件
                                if not self.tool_manager._should_ignore_file(file_path):
                                    self.modified_source_files.add(file_path)
                                    self.log(f"记录源文件修改: {file_path}")

                                    # 检查是否修改了关键文件
                                    if self._is_critical_file(file_path):
                                        self.important_modifications_detected = True
                                        self.log(f"检测到关键文件修改: {file_path}")

                self.last_git_status = current_git_status

        except Exception as e:
            self.log(f"跟踪文件修改时出错: {e}", "WARNING")

    def _is_critical_file(self, file_path: str) -> bool:
        """判断是否是关键源文件"""
        critical_patterns = [
            'astropy/',
            'src/',
            'lib/',
            '/core/',
            '/modeling/',
            '/separable.py'
        ]

        for pattern in critical_patterns:
            if pattern in file_path:
                return True
        return False

    def check_early_completion(self) -> Dict[str, Any]:
        """检查是否可以提前完成任务"""
        # 检查是否有重要的源文件修改
        if not self.important_modifications_detected:
            return None

        # 检查当前是否接近迭代限制
        if self.iteration_count < self.max_iterations - 5:  # 至少还有5轮时才检查
            return None

        # 尝试生成当前的git diff
        current_diff = self.tool_manager._generate_git_diff()

        if current_diff and self.tool_manager._validate_diff_quality(current_diff):
            self.log(f"检测到高质量修改，考虑提前完成")
            self.log(f"修改的源文件: {list(self.modified_source_files)}")

            # 可以考虑运行简单的验证测试
            if self._validate_modifications():
                return {
                    "success": True,
                    "patch": current_diff,
                    "iterations": self.iteration_count,
                    "log_file": str(self.log_file),
                    "llm_log_file": str(self.llm_interaction_log_file),
                    "early_completion": True,
                    "reason": "检测到有效的关键文件修改"
                }

        return None

    def _validate_modifications(self) -> bool:
        """验证当前修改的有效性"""
        try:
            # 检查修改的文件是否能正常导入（基本语法检查）
            for file_path in self.modified_source_files:
                if file_path.endswith('.py'):
                    # 简单的语法检查
                    check_result = self.tool_manager.bash_tool.execute(
                        f"cd /testbed && python -m py_compile '{file_path}' 2>/dev/null && echo 'OK' || echo 'ERROR'"
                    )
                    if 'ERROR' in check_result:
                        self.log(f"文件 {file_path} 语法检查失败", "WARNING")
                        return False
                    else:
                        self.log(f"文件 {file_path} 语法检查通过")

            # 如果所有修改的文件都通过语法检查
            return True

        except Exception as e:
            self.log(f"验证修改时出错: {e}", "WARNING")
            return False

    def enhanced_generate_final_diff(self) -> str:
        """增强的最终diff生成，确保捕获所有重要修改"""
        self.log("开始生成最终git diff...")

        try:
            # 先更新文件修改跟踪
            self.track_file_modifications()

            # 记录修改统计
            if self.modified_source_files:
                self.log(f"跟踪到的源文件修改: {list(self.modified_source_files)}")

            # 确保所有重要文件都被添加到git
            for file_path in self.modified_source_files:
                if not self.tool_manager._should_ignore_file(file_path):
                    add_result = self.tool_manager.bash_tool.execute(f"cd /testbed && git add '{file_path}'")
                    self.log(f"添加文件到git: {file_path}")

            # 生成最终的diff
            final_diff = self.tool_manager._generate_git_diff()

            if final_diff:
                self.log(f"成功生成最终git diff，长度: {len(final_diff)} 字符")

                # 验证diff质量
                if self.tool_manager._validate_diff_quality(final_diff):
                    self.log("最终git diff质量验证通过")
                else:
                    self.log("最终git diff质量验证失败", "WARNING")

                return final_diff
            else:
                self.log("未能生成最终git diff", "WARNING")
                return ""

        except Exception as e:
            self.log(f"生成最终diff时出错: {e}", "ERROR")
            return ""

    def _convert_to_serializable(self, obj):
        """将复杂对象转换为可JSON序列化的基本数据结构"""
        if obj is None:
            return None
        elif isinstance(obj, (str, int, float, bool)):
            return obj
        elif isinstance(obj, list):
            return [self._convert_to_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {key: self._convert_to_serializable(value) for key, value in obj.items()}
        elif hasattr(obj, '__dict__'):
            try:
                result = {}
                for key, value in obj.__dict__.items():
                    if not key.startswith('_'):
                        result[key] = self._convert_to_serializable(value)
                return result
            except:
                return str(obj)
        else:
            return str(obj)

    def log_llm_interaction(self, messages: List[Dict[str, Any]], response: Any, iteration: int):
        """记录LLM交互的详细信息 - 只保存最后一轮"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 安全获取usage信息
        usage_info = None
        try:
            if hasattr(response, 'usage') and response.usage:
                usage_info = {
                    "prompt_tokens": getattr(response.usage, 'prompt_tokens', 0),
                    "completion_tokens": getattr(response.usage, 'completion_tokens', 0),
                    "total_tokens": getattr(response.usage, 'total_tokens', 0)
                }
        except Exception as e:
            self.log(f"获取usage信息失败: {e}", "WARNING")

        interaction_log = {
            "timestamp": timestamp,
            "iteration": iteration,
            "model": self.model_name,
            "messages": self._convert_to_serializable(messages),
            "response": self._convert_to_serializable(response),
            "usage": usage_info
        }

        # 覆盖写入（只保存最后一轮）
        with open(self.llm_interaction_log_file, "w", encoding="utf-8") as f:
            json.dump(interaction_log, f, ensure_ascii=False, indent=2)

    def _call_llm_with_retry(self, messages: List[Dict[str, Any]]) -> Any:
        """带重试机制的LLM调用"""
        for attempt in range(self.max_retries + 1):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    tools=self.tools,
                    tool_choice="auto",
                    temperature=0.1
                )
                return response
            except Exception as e:
                if attempt == self.max_retries or not self._should_retry(e):
                    raise e

                backoff_time = self._calculate_backoff(attempt)
                self.log(f"LLM调用失败 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}", "WARNING")
                self.log(f"等待 {backoff_time:.2f} 秒后重试...", "INFO")
                time.sleep(backoff_time)


    def call_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> str:
        """调用工具"""
        return self.tool_manager.call_tool(tool_name, tool_args)

    def run(self, problem_statement: str, instance_index: int = 0) -> Dict[str, Any]:
        """运行agent解决问题"""
        try:
            # 启动Docker容器
            self.log("启动Docker容器...")
            start_result = self.docker_manager.start_container()
            self.log(start_result)

            if not self.docker_manager.is_running:
                return {
                    "success": False,
                    "error": f"无法启动Docker容器: {start_result}",
                    "log_file": str(self.log_file)
                }

            # 获取初始环境信息
            bash_tool = self.tool_manager.bash_tool
            env_info = bash_tool.execute("pwd && ls -la")
            self.log(f"初始环境信息:\n{env_info}")

            # 初始化对话 - 使用CSV数据或默认系统提示词
            system_message = self.get_system_prompt(instance_index, env_info)

            self.conversation_history = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": problem_statement}
            ]

            # 主循环
            while self.iteration_count < self.max_iterations:
                self.iteration_count += 1
                self.log(f"=== 第 {self.iteration_count} 轮迭代 ===")

                try:
                    # 跟踪文件修改状态
                    self.track_file_modifications()

                    # 检查是否可以提前完成
                    early_completion = self.check_early_completion()
                    if early_completion:
                        self.log("检测到满足提前完成条件")
                        return early_completion

                    # 调用LLM
                    response = self._call_llm_with_retry(self.conversation_history)

                    # 记录LLM交互
                    self.log_llm_interaction(self.conversation_history, response, self.iteration_count)

                    # 获取响应消息
                    message = response.choices[0].message

                    # 添加助手消息到对话历史
                    self.conversation_history.append({
                        "role": "assistant",
                        "content": message.content or "",
                        "tool_calls": [self._convert_to_serializable(tc) for tc in message.tool_calls] if message.tool_calls else None
                    })

                    # 输出助手响应
                    if message.content:
                        self.log(f"🤖 [助手响应] {message.content[:200]}{'...' if len(message.content) > 200 else ''}")

                    # 处理工具调用
                    if message.tool_calls:
                        tool_results = []

                        for tool_call in message.tool_calls:
                            tool_name = tool_call.function.name
                            tool_args = json.loads(tool_call.function.arguments)

                            # 执行工具（详细日志在call_tool方法中）
                            result = self.call_tool(tool_name, tool_args)

                            # 添加工具结果到对话历史
                            tool_results.append({
                                "tool_call_id": tool_call.id,
                                "role": "tool",
                                "name": tool_name,
                                "content": result
                            })

                            # 检查是否提交了结果
                            if tool_name == "submit":
                                self.log("任务已提交，完成执行")
                                return {
                                    "success": True,
                                    "result": result,
                                    "patch": self.tool_manager.generated_patch,
                                    "iterations": self.iteration_count,
                                    "log_file": str(self.log_file),
                                    "llm_log_file": str(self.llm_interaction_log_file)
                                }

                        # 将所有工具结果添加到对话历史
                        self.conversation_history.extend(tool_results)

                    else:
                        # 没有工具调用，可能是思考或询问
                        if message.content:
                            # 如果助手有回复但没有工具调用，继续对话
                            continue
                        else:
                            self.log("助手没有回复或工具调用，结束对话")
                            break

                except Exception as e:
                    self.log(f"第 {self.iteration_count} 轮迭代出错: {str(e)}", "ERROR")
                    self.conversation_history.append({
                        "role": "user",
                        "content": f"上一步操作出现错误: {str(e)}，请重新尝试。"
                    })

            # 达到最大迭代次数，使用增强的diff生成
            self.log(f"达到最大迭代次数 {self.max_iterations}，任务未完成", "WARNING")
            self.log("使用增强方法生成git diff补丁...")

            # 最后一次跟踪文件修改
            self.track_file_modifications()

            # 使用增强的git diff生成方法
            git_diff = self.enhanced_generate_final_diff()

            # 评估任务完成情况
            task_success = False
            if git_diff and self.important_modifications_detected:
                self.log("检测到重要修改，可能已部分完成任务")
                task_success = self._validate_modifications()
                if task_success:
                    self.log("修改验证通过，标记为部分成功")

            if git_diff:
                self.log(f"成功生成git diff补丁，长度: {len(git_diff)} 字符")
                self.log(f"修改的源文件数量: {len(self.modified_source_files)}")
                self.log(f"检测到重要修改: {self.important_modifications_detected}")

                return {
                    "success": task_success,  # 如果有有效修改，可以标记为部分成功
                    "error": "达到最大迭代次数" if not task_success else "达到最大迭代次数但检测到有效修改",
                    "patch": git_diff,
                    "iterations": self.iteration_count,
                    "log_file": str(self.log_file),
                    "llm_log_file": str(self.llm_interaction_log_file),
                    "modified_files": list(self.modified_source_files),
                    "has_critical_modifications": self.important_modifications_detected
                }
            else:
                self.log("未检测到文件更改，无法生成补丁", "WARNING")
                return {
                    "success": False,
                    "error": "达到最大迭代次数且无有效修改",
                    "patch": "",
                    "iterations": self.iteration_count,
                    "log_file": str(self.log_file),
                    "llm_log_file": str(self.llm_interaction_log_file),
                    "modified_files": list(self.modified_source_files),
                    "has_critical_modifications": self.important_modifications_detected
                }

        except Exception as e:
            self.log(f"执行过程中发生错误: {str(e)}", "ERROR")
            return {
                "success": False,
                "error": str(e),
                "log_file": str(self.log_file)
            }

        finally:
            # 清理Docker容器
            try:
                cleanup_result = self.docker_manager.stop_container()
                self.log(cleanup_result)
            except Exception as e:
                self.log(f"清理容器时出错: {str(e)}", "WARNING")
