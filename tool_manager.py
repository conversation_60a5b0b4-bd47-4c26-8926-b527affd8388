"""
工具管理器模块
统一管理所有工具的调用逻辑，提供清晰的接口
"""

import json
import logging
from typing import Dict, Any, Optional
from docker_tools import DockerManager, DockerStrReplaceEditor, DockerBashTool

logger = logging.getLogger(__name__)


class ToolManager:
    """工具管理器，负责初始化和调用各种工具"""

    def __init__(self, docker_manager: DockerManager):
        """
        初始化工具管理器

        Args:
            docker_manager: Docker容器管理器
        """
        self.docker_manager = docker_manager
        self.str_editor = DockerStrReplaceEditor(docker_manager)
        self.bash_tool = DockerBashTool(docker_manager)

        # 存储生成的补丁
        self.generated_patch = ""

        # 工具调用映射
        self._tool_handlers = {
            "str_replace_editor": self._handle_str_replace_editor,
            "bash": self._handle_bash,
            "submit": self._handle_submit
        }

    def call_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> str:
        """
        调用指定工具

        Args:
            tool_name: 工具名称
            tool_args: 工具参数

        Returns:
            工具执行结果
        """
        logger.info(f"🔧 [工具调用] 工具名称: {tool_name}")
        logger.info(f"🔧 [工具参数] {json.dumps(tool_args, ensure_ascii=False)[:200]}")

        try:
            if tool_name not in self._tool_handlers:
                result = f"错误: 未知工具 '{tool_name}'"
                logger.error(result)
                return result

            result = self._tool_handlers[tool_name](tool_args)
            logger.info(f"🔧 [工具返回] {tool_name}: {str(result)[:100]}...")
            return result

        except Exception as e:
            result = f"工具调用错误: {str(e)}"
            logger.error(f"🔧 [工具返回] 异常: {str(result)[:100]}...")
            return result

    def _handle_str_replace_editor(self, tool_args: Dict[str, Any]) -> str:
        """处理文件编辑工具调用"""
        return self.str_editor.execute(**tool_args)

    def _handle_bash(self, tool_args: Dict[str, Any]) -> str:
        """处理bash命令工具调用"""
        return self.bash_tool.execute(tool_args["command"])

    def _handle_submit(self, tool_args: Dict[str, Any]) -> str:
        """处理任务提交工具调用 - 优化版本"""
        try:
            # 使用优化的git diff生成方法
            git_diff = self._generate_git_diff()

            # 存储补丁到实例变量中
            self.generated_patch = git_diff.strip()

            if git_diff.strip():
                # 验证diff质量
                quality_check = self._validate_diff_quality(git_diff)
                quality_status = "✅ 高质量" if quality_check else "⚠️ 质量待检"

                return f"任务已提交: {tool_args['summary']}\n\n生成的补丁 ({len(git_diff)} 字符) {quality_status}:\n{git_diff[:500]}{'...' if len(git_diff) > 500 else ''}"
            else:
                # 如果没有生成补丁，记录调试信息
                logger.warning("Submit时未能生成git diff，记录调试信息")
                self._log_debug_info()
                return f"任务已提交: {tool_args['summary']}\n⚠️ 未检测到文件更改\n建议检查是否有实际的代码修改"

        except Exception as e:
            self.generated_patch = ""
            error_msg = str(e)
            logger.error(f"处理submit时出错: {error_msg}")
            # 记录详细的调试信息
            self._log_debug_info()
            return f"任务已提交: {tool_args['summary']}\n❌ 无法生成补丁: {error_msg}\n已记录调试信息到日志"

    def _generate_git_diff(self) -> str:
        """生成git diff补丁 - 优化版本，确保捕获源代码修改"""
        try:
            # 检查工作目录状态
            status_output = self.bash_tool.execute("cd /testbed && git status --porcelain")
            logger.info(f"Git 状态: {status_output}")

            # 分析修改的文件类型
            modified_files = []
            if status_output.strip():
                for line in status_output.strip().split('\n'):
                    if line.strip():
                        file_path = line.split()[-1] if line.split() else ""
                        if file_path:
                            modified_files.append(file_path)

            logger.info(f"检测到修改的文件: {modified_files}")

            # 优先处理源代码文件的修改
            source_files = [f for f in modified_files if f.endswith('.py') and not self._should_ignore_file(f)]
            logger.info(f"源代码文件修改: {source_files}")

            git_diff = ""

            # 方法1: 优先添加源代码文件并生成diff
            if source_files:
                # 只添加重要的源代码文件
                for file in source_files:
                    self.bash_tool.execute(f"cd /testbed && git add '{file}'")

                git_diff = self.bash_tool.execute("cd /testbed && git diff --cached")
                logger.info(f"方法1 - 源代码cached diff 长度: {len(git_diff)}")

                if git_diff.strip():
                    # 验证diff质量
                    if self._validate_diff_quality(git_diff):
                        return git_diff.strip()

            # 方法2: 如果方法1失败，尝试添加所有非临时文件
            if not git_diff.strip():
                # 过滤掉明显的临时文件和测试文件
                important_files = [f for f in modified_files if not self._should_ignore_file(f)]

                if important_files:
                    for file in important_files:
                        self.bash_tool.execute(f"cd /testbed && git add '{file}'")

                    git_diff = self.bash_tool.execute("cd /testbed && git diff --cached")
                    logger.info(f"方法2 - 重要文件cached diff 长度: {len(git_diff)}")

            # 方法3: 直接diff HEAD（捕获所有修改）
            if not git_diff.strip():
                git_diff = self.bash_tool.execute("cd /testbed && git diff HEAD")
                logger.info(f"方法3 - HEAD diff 长度: {len(git_diff)}")

            # 方法4: 显示所有未跟踪的重要文件
            if not git_diff.strip():
                untracked = self.bash_tool.execute("cd /testbed && git ls-files --others --exclude-standard")
                if untracked.strip():
                    important_untracked = []
                    for file_path in untracked.strip().split('\n'):
                        file_path = file_path.strip()
                        if file_path and not self._should_ignore_file(file_path):
                            important_untracked.append(file_path)

                    if important_untracked:
                        git_diff = f"# 新增重要文件:\n" + "\n".join(important_untracked) + "\n\n# 文件内容:\n"
                        for file_path in important_untracked:
                            try:
                                content = self.bash_tool.execute(f"cd /testbed && cat '{file_path}'")
                                git_diff += f"\n--- /dev/null\n+++ {file_path}\n{content}\n"
                            except Exception as e:
                                logger.warning(f"读取文件 {file_path} 失败: {e}")
                                continue

                        logger.info(f"方法4 - 重要未跟踪文件 diff 长度: {len(git_diff)}")

            # 最终验证和日志记录
            if git_diff.strip():
                logger.info(f"成功生成git diff，总长度: {len(git_diff)} 字符")
                if source_files:
                    logger.info(f"包含源代码文件修改: {source_files}")
            else:
                logger.warning("未能生成任何有效的git diff")
                # 记录详细的调试信息
                self._log_debug_info()

            return git_diff.strip()

        except Exception as e:
            logger.error(f"生成git diff时出错: {e}")
            self._log_debug_info()
            return ""

    def generate_git_diff_filtered(self) -> str:
        """生成git diff补丁，过滤debug和test文件"""
        try:
            # 检查工作目录状态
            status_output = self.bash_tool.execute("cd /testbed && git status --porcelain")
            logger.info(f"Git 状态: {status_output}")

            # 有选择性地添加文件并生成cached diff（排除debug和test文件）
            add_cmd = (
                "cd /testbed && git add -A && "
                "git reset HEAD -- '*test*' '*debug*' 'test_*' 'debug_*' '**/test*' '**/debug*' "
                "'*_test.py' '*_debug.py' 'test*.py' 'debug*.py' 2>/dev/null || true && "
                "git diff --cached"
            )
            git_diff = self.bash_tool.execute(add_cmd)
            logger.info(f"过滤后的diff长度: {len(git_diff)}")

            if not git_diff.strip():
                # 如果没有diff，尝试显示未跟踪文件（过滤debug/test）
                untracked = self.bash_tool.execute("cd /testbed && git ls-files --others --exclude-standard")
                if untracked.strip():
                    filtered_files = []
                    for file_path in untracked.strip().split('\n'):
                        file_path = file_path.strip()
                        if file_path and not self._should_ignore_file(file_path):
                            filtered_files.append(file_path)

                    if filtered_files:
                        git_diff = f"# 新增文件 (已过滤debug/test):\n" + "\n".join(filtered_files) + "\n\n# 文件内容:\n"
                        for file_path in filtered_files:
                            try:
                                content = self.bash_tool.execute(f"cd /testbed && cat '{file_path}'")
                                git_diff += f"\n--- /dev/null\n+++ {file_path}\n{content}\n"
                            except Exception as e:
                                logger.warning(f"读取文件 {file_path} 失败: {e}")
                                continue

            return git_diff.strip()

        except Exception as e:
            logger.error(f"生成过滤后的git diff时出错: {e}")
            return ""

    def _should_ignore_file(self, file_path: str) -> bool:
        """判断文件是否应该被忽略（debug和test相关文件）- 优化版本"""
        file_path_lower = file_path.lower()

        # 检查文件名模式
        ignore_patterns = [
            'test_', 'debug_', '_test', '_debug',
            'test.', 'debug.', '.test', '.debug',
            '/test', '/debug', 'test/', 'debug/',
            '__pycache__', '.pyc', '.tmp', '.log',
            'temp_', '_temp', 'temporary'
        ]

        for pattern in ignore_patterns:
            if pattern in file_path_lower:
                return True

        # 检查文件扩展名前的关键词
        basename = file_path.split('/')[-1].lower()
        if (basename.startswith('test') or basename.startswith('debug') or
            basename.endswith('test.py') or basename.endswith('debug.py') or
            'test_' in basename or 'debug_' in basename or
            basename.startswith('temp') or basename.endswith('.tmp')):
            return True

        # 特别保护关键源代码路径
        important_paths = [
            'astropy/', 'src/', 'lib/', 'core/',
        ]

        for path in important_paths:
            if path in file_path and file_path.endswith('.py'):
                # 这是重要的源代码文件，不应该被忽略
                return False

        return False

    def _validate_diff_quality(self, git_diff: str) -> bool:
        """验证git diff的质量，确保包含有意义的修改"""
        if not git_diff.strip():
            return False

        # 检查diff长度（太短可能不是有意义的修改）
        if len(git_diff) < 50:
            logger.warning(f"Git diff太短 ({len(git_diff)} 字符)，可能不是有效修改")
            return False

        # 检查是否包含实际的代码修改（+ 或 - 行）
        has_additions = '+' in git_diff and any(line.startswith('+') and not line.startswith('+++') for line in git_diff.split('\n'))
        has_deletions = '-' in git_diff and any(line.startswith('-') and not line.startswith('---') for line in git_diff.split('\n'))

        if not (has_additions or has_deletions):
            logger.warning("Git diff不包含实际的代码修改行")
            return False

        # 检查是否修改了Python源文件
        python_files_modified = any('.py' in line for line in git_diff.split('\n')
                                   if line.startswith('+++') or line.startswith('---'))

        if python_files_modified:
            logger.info("Git diff包含Python源文件修改，质量验证通过")
            return True

        # 如果没有Python文件修改，检查修改的行数
        modification_lines = [line for line in git_diff.split('\n')
                            if line.startswith('+') or line.startswith('-')]

        if len(modification_lines) >= 5:  # 至少5行修改
            logger.info(f"Git diff包含足够的修改行 ({len(modification_lines)} 行)")
            return True

        logger.warning(f"Git diff质量不足：只有 {len(modification_lines)} 行修改")
        return False

    def _log_debug_info(self):
        """记录详细的调试信息，帮助诊断git diff生成问题"""
        try:
            logger.info("=== Git Diff 调试信息 ===")

            # 1. 工作目录状态
            status = self.bash_tool.execute("cd /testbed && git status")
            logger.info(f"完整Git状态:\n{status}")

            # 2. 修改的文件列表
            modified_files = self.bash_tool.execute("cd /testbed && git ls-files --modified")
            if modified_files.strip():
                logger.info(f"修改的文件:\n{modified_files}")
            else:
                logger.warning("没有检测到修改的文件")

            # 3. 未跟踪的文件
            untracked = self.bash_tool.execute("cd /testbed && git ls-files --others --exclude-standard")
            if untracked.strip():
                logger.info(f"未跟踪的文件:\n{untracked}")

            # 4. 工作区与暂存区的差异
            workspace_diff = self.bash_tool.execute("cd /testbed && git diff")
            if workspace_diff.strip():
                logger.info(f"工作区差异长度: {len(workspace_diff)} 字符")
                # 只记录前200字符避免日志过长
                logger.info(f"工作区差异预览:\n{workspace_diff[:200]}...")

            # 5. 暂存区与HEAD的差异
            staged_diff = self.bash_tool.execute("cd /testbed && git diff --cached")
            if staged_diff.strip():
                logger.info(f"暂存区差异长度: {len(staged_diff)} 字符")
                logger.info(f"暂存区差异预览:\n{staged_diff[:200]}...")

            # 6. 检查是否是git仓库
            git_check = self.bash_tool.execute("cd /testbed && git rev-parse --is-inside-work-tree 2>/dev/null || echo 'not a git repo'")
            logger.info(f"Git仓库检查: {git_check.strip()}")

            logger.info("=== 调试信息结束 ===")

        except Exception as e:
            logger.error(f"记录调试信息时出错: {e}")
